// Part List Management
document.addEventListener('DOMContentLoaded', function() {
    let currentPage = 1;
    let currentSearch = '';
    let partTypes = [];
    let editingRow = null;

    // Initialize
    init();

    function init() {
        loadPartTypes();
        loadPartsData();
        setupEventListeners();
    }

    function setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentSearch = this.value;
                    currentPage = 1;
                    loadPartsData();
                }, 500);
            });
        }
    }

    function loadPartTypes() {
        fetch('/sales/part-list/part-types')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    partTypes = data.data;
                }
            })
            .catch(error => {
                console.error('Error loading part types:', error);
            });
    }

    function loadPartsData() {
        showLoading(true);

        const params = new URLSearchParams({
            page: currentPage,
            search: currentSearch
        });

        fetch(`/sales/part-list/data?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderPartsTable(data.data);
                    renderPagination(data.pagination);
                    updateShowingText(data.pagination);
                } else {
                    showError('Gagal memuat data part');
                }
            })
            .catch(error => {
                console.error('Error loading parts:', error);
                showError('Terjadi kesalahan saat memuat data');
            })
            .finally(() => {
                showLoading(false);
            });
    }

    function renderPartsTable(parts) {
        const tbody = document.getElementById('parts-table-body');
        if (!tbody) return;

        if (parts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center text-muted">
                        <i class="mdi mdi-package-variant-closed"></i><br>
                        Tidak ada data part ditemukan
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = parts.map(part => `
            <tr data-part-code="${part.part_code}">
                <td class="readonly-field">${part.part_code}</td>
                <td>
                    <input type="text" class="editable-field" data-field="part_name" value="${part.part_name || ''}" />
                </td>
                <td>
                    <select class="editable-field" data-field="part_type">
                        ${partTypes.map(type => `
                            <option value="${type}" ${part.part_type === type ? 'selected' : ''}>${type}</option>
                        `).join('')}
                    </select>
                </td>
                <td class="currency">
                    <input type="number" class="editable-field" data-field="price" value="${part.price || ''}" step="0.01" min="0" />
                </td>
                <td class="currency">
                    <input type="number" class="editable-field" data-field="purchase_price" value="${part.purchase_price || ''}" step="0.01" min="0" />
                </td>
                <td>
                    <input type="text" class="editable-field" data-field="eum" value="${part.eum || ''}" maxlength="5" />
                </td>
                <td class="stock-info">
                    ${renderStockInfo(part.stock_info)}
                </td>
                <td class="text-center">
                    <span class="badge badge-info">${part.total_stock}</span>
                </td>
                <td class="action-buttons">
                    <button class="btn-edit" onclick="editRow('${part.part_code}')">
                        <i class="mdi mdi-pencil"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    function renderStockInfo(stockInfo) {
        if (!stockInfo || stockInfo.length === 0) {
            return '<span class="text-muted">Tidak ada stok</span>';
        }

        return stockInfo.map(stock => `
            <div class="stock-item">
                <span class="stock-site">${stock.site_name}:</span>
                <span class="stock-quantity">${stock.stock_quantity}</span>
                ${stock.site_part_name ?
                    `<br><small class="text-muted">(${stock.site_part_name})</small>` : ''}
            </div>
        `).join('');
    }

    function renderPagination(pagination) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        let paginationHTML = '';

        // Previous button
        if (pagination.current_page > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${pagination.current_page - 1})">
                        <i class="mdi mdi-chevron-left"></i>
                    </a>
                </li>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        }

        // Next button
        if (pagination.current_page < pagination.last_page) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${pagination.current_page + 1})">
                        <i class="mdi mdi-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    function updateShowingText(pagination) {
        const showingText = document.getElementById('showing-text');
        if (showingText) {
            showingText.textContent = `Menampilkan ${pagination.from || 0} - ${pagination.to || 0} dari ${pagination.total} part`;
        }
    }

    // Global functions
    window.changePage = function(page) {
        currentPage = page;
        loadPartsData();
    };

    window.editRow = function(partCode) {
        if (editingRow && editingRow !== partCode) {
            cancelEdit();
        }

        editingRow = partCode;
        const row = document.querySelector(`tr[data-part-code="${partCode}"]`);
        if (!row) return;

        // Change edit button to save/cancel buttons
        const actionCell = row.querySelector('.action-buttons');
        actionCell.innerHTML = `
            <button class="btn-save" onclick="saveRow('${partCode}')">
                <i class="mdi mdi-check"></i>
            </button>
            <button class="btn-cancel" onclick="cancelEdit()">
                <i class="mdi mdi-close"></i>
            </button>
        `;

        // Enable editing
        const editableFields = row.querySelectorAll('.editable-field');
        editableFields.forEach(field => {
            field.disabled = false;
            field.style.backgroundColor = '#fff';
        });
    };

    window.saveRow = function(partCode) {
        const row = document.querySelector(`tr[data-part-code="${partCode}"]`);
        if (!row) return;

        // Collect form data
        const formData = {};
        const editableFields = row.querySelectorAll('.editable-field');
        editableFields.forEach(field => {
            const fieldName = field.getAttribute('data-field');
            formData[fieldName] = field.value;
        });

        // Validate required fields
        if (!formData.part_name || !formData.part_type) {
            showError('Nama part dan tipe part harus diisi');
            return;
        }

        showLoading(true);

        // Send update request
        fetch(`/sales/part-list/${partCode}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                editingRow = null;
                loadPartsData(); // Reload to show updated data
            } else {
                if (data.errors) {
                    const errorMessages = Object.values(data.errors).flat().join('<br>');
                    showError(errorMessages);
                } else {
                    showError(data.message || 'Gagal menyimpan perubahan');
                }
            }
        })
        .catch(error => {
            console.error('Error saving part:', error);
            showError('Terjadi kesalahan saat menyimpan data');
        })
        .finally(() => {
            showLoading(false);
        });
    };

    window.cancelEdit = function() {
        editingRow = null;
        loadPartsData(); // Reload to reset form
    };

    function showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.toggle('d-none', !show);
        }
    }

    function showSuccess(message) {
        // Using SweetAlert if available, otherwise alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: message,
                timer: 3000,
                showConfirmButton: false
            });
        } else {
            alert(message);
        }
    }

    function showError(message) {
        // Using SweetAlert if available, otherwise alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                html: message,
                confirmButtonText: 'OK'
            });
        } else {
            alert(message);
        }
    }
});
