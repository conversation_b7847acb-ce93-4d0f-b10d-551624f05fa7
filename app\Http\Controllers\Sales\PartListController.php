<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\Controller;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PartListController extends Controller
{
    /**
     * Display the part list page
     */
    public function index()
    {
        return view('sales.part-list');
    }

    /**
     * Get parts data for AJAX request
     */
    public function getData(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $perPage = 10;
            $search = $request->get('search', '');

            // Build query with search functionality
            $query = Part::with(['partInventories.site'])
                ->orderBy('part_name', 'asc');

            // Apply search filter
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('part_name', 'LIKE', "%{$search}%")
                      ->orWhere('part_code', 'LIKE', "%{$search}%")
                      ->orWhere('part_type', 'LIKE', "%{$search}%");
                });
            }

            // Get paginated results
            $parts = $query->paginate($perPage, ['*'], 'page', $page);

            // Format data for response
            $formattedParts = $parts->getCollection()->map(function ($part) {
                // Get stock information for all sites
                $stockInfo = [];
                $totalStock = 0;
                
                foreach ($part->partInventories as $inventory) {
                    $stockInfo[] = [
                        'site_name' => $inventory->site->site_name ?? 'Unknown',
                        'site_id' => $inventory->site_id,
                        'stock_quantity' => $inventory->stock_quantity ?? 0,
                        'site_part_name' => $inventory->site_part_name
                    ];
                    $totalStock += $inventory->stock_quantity ?? 0;
                }

                return [
                    'part_code' => $part->part_code,
                    'part_name' => $part->part_name,
                    'part_type' => $part->part_type,
                    'price' => $part->price,
                    'purchase_price' => $part->purchase_price,
                    'eum' => $part->eum,
                    'bin_location' => $part->bin_location,
                    'stock_info' => $stockInfo,
                    'total_stock' => $totalStock,
                    'created_at' => $part->created_at,
                    'updated_at' => $part->updated_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedParts,
                'pagination' => [
                    'current_page' => $parts->currentPage(),
                    'last_page' => $parts->lastPage(),
                    'per_page' => $parts->perPage(),
                    'total' => $parts->total(),
                    'from' => $parts->firstItem(),
                    'to' => $parts->lastItem()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching parts data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data part'
            ], 500);
        }
    }

    /**
     * Update part data
     */
    public function update(Request $request, $partCode)
    {
        try {
            // Validation rules
            $validator = Validator::make($request->all(), [
                'part_name' => 'required|string|max:255',
                'part_type' => 'required|in:AC,TYRE,FABRIKASI,PERLENGKAPAN AC,PERSEDIAAN LAINNYA',
                'price' => 'nullable|numeric|min:0',
                'purchase_price' => 'nullable|numeric|min:0',
                'eum' => 'nullable|string|max:5'
            ], [
                'part_name.required' => 'Nama part harus diisi',
                'part_name.max' => 'Nama part maksimal 255 karakter',
                'part_type.required' => 'Tipe part harus dipilih',
                'part_type.in' => 'Tipe part tidak valid',
                'price.numeric' => 'Harga harus berupa angka',
                'price.min' => 'Harga tidak boleh negatif',
                'purchase_price.numeric' => 'Harga beli harus berupa angka',
                'purchase_price.min' => 'Harga beli tidak boleh negatif',
                'eum.max' => 'EUM maksimal 5 karakter'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Find the part
            $part = Part::where('part_code', $partCode)->first();
            if (!$part) {
                return response()->json([
                    'success' => false,
                    'message' => 'Part tidak ditemukan'
                ], 404);
            }

            // Store old part name for comparison
            $oldPartName = $part->part_name;
            $newPartName = $request->part_name;

            // Update part data
            $part->update([
                'part_name' => $newPartName,
                'part_type' => $request->part_type,
                'price' => $request->price,
                'purchase_price' => $request->purchase_price,
                'eum' => $request->eum
            ]);

            // Critical Business Rule: Update site inventories if part name changed
            if ($oldPartName !== $newPartName) {
                PartInventory::where('part_code', $partCode)
                    ->whereNull('site_part_name') // Only update if no custom site name
                    ->update(['site_part_name' => $newPartName]);
                
                Log::info("Part name updated from '{$oldPartName}' to '{$newPartName}' for part {$partCode}. Site inventories synchronized.");
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data part berhasil diperbarui',
                'data' => $part->fresh()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating part: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data part'
            ], 500);
        }
    }

    /**
     * Get part types for dropdown
     */
    public function getPartTypes()
    {
        return response()->json([
            'success' => true,
            'data' => Part::PART_TYPES
        ]);
    }
}
